# Docker Environment for Laravel/Inertia.js/React.js/PostgreSQL Teaching

## Latest Service Versions (Updated: June 2025)

### Core Services

| Service | Latest Version | Notes |
|---------|---------------|-------|
| **PHP** | 8.4.8 | Current stable release |
| **Node.js** | 22.16.0 (LTS) | Long Term Support version |
| **PostgreSQL** | 17.5 | Latest stable release |
| **Nginx** | 1.28.0 | Stable version |
| **Composer** | 2.8.9 | Latest stable release |
| **Laravel** | 11.x | Current stable version |
| **Redis** | 8.0 | Latest stable release |

### Additional Tools

| Tool | Latest Version | Purpose |
|------|---------------|---------|
| **npm** | Included with Node.js 22.16.0 | Package manager for JavaScript |
| **Vite** | Latest via npm | Frontend build tool |
| **React** | Latest via npm | Frontend framework |
| **Inertia.js** | Latest via npm | Modern monolith approach |

### Docker Images to Use

```yaml
# Recommended Docker images for docker-compose.yml
services:
  php:
    image: php:8.4-fpm
    
  nginx:
    image: nginx:1.28.0
    
  postgres:
    image: postgres:17.5
    
  node:
    image: node:22.16.0-alpine
    
  redis:
    image: redis:8.0-alpine
```

### Version Notes

- **PHP 8.4**: Latest stable with improved performance and new features
- **Node.js 22.16.0**: LTS version ensuring long-term stability
- **PostgreSQL 17.5**: Latest with enhanced performance and security
- **Laravel 11**: Streamlined application structure and modern features
- **Redis 8.0**: Latest with improved memory efficiency and new capabilities

### For Students

This environment provides:
- ✅ Consistent development environment across all operating systems
- ✅ Latest stable versions of all technologies
- ✅ Production-ready configuration
- ✅ Easy setup with single `docker-compose up` command
- ✅ No local installation conflicts

### Quick Start

1. Install Docker Desktop on your system
2. Clone the project repository
3. Run `docker-compose up -d`
4. Access the application at `http://localhost`

---

*Last updated: June 2025*
*All versions verified from official sources*
